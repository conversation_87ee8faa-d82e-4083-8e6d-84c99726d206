const roleService = require('../../services/admin/role-service')
const { jsonResponse } = require('../../common/helper')
const { paginate } = require('../../utils/pagination')

class RoleHandler {
  async createRole(req) {
    try {
      const body = await req.json()
      const result = await roleService.createRole(body)
      return jsonResponse(result, 201)
    } catch (err) {
      console.error('Error:', err.message)
      if (
        err.message.includes('already exists') ||
        err.message.includes('Description')
      ) {
        return jsonResponse({ error: err.message }, 400)
      }
      return jsonResponse({ error: 'Internal Server Error' }, 500)
    }
  }

  async editRole(req) {
    try {
      const body = await req.json()
      const result = await roleService.editRole(body)
      return jsonResponse(result, 200)
    } catch (err) {
      return jsonResponse('Error editing role', 500)
    }
  }

  async deleteRole(req) {
    try {
      const roleId = req.query.get('roleId')
      if (!roleId) {
        return jsonResponse('Role ID is required', 400)
      }
      const result = await roleService.deleteRole(roleId)
      return jsonResponse(result, 200)
    } catch (err) {
      return jsonResponse('Error deleting role', 500)
    }
  }

  async listRoles(req) {
    try {
      const organizationId = req.query.get('organizationId')
      const search = req.query.get('searchText') || ''
      const pageSize = req.query.get('pageSize')
        ? parseInt(req.query.get('pageSize'))
        : null
      const page = req.query.get('page')
        ? parseInt(req.query.get('page'))
        : null

      if (!organizationId) {
        return jsonResponse('Organization ID is required', 400)
      }

      const roles = await roleService.listRoles(organizationId, search)

      // If no pagination parameters provided, return all roles
      if (pageSize === null && page === null) {
        return jsonResponse(
          {
            items: roles,
            totalItemCount: roles.length,
            currentPage: 1,
            totalPages: 1,
          },
          200,
        )
      }

      // Apply pagination if parameters are provided
      const finalPageSize = pageSize || 10
      const finalPage = page || 1
      const paginatedResult = paginate(roles, finalPageSize, finalPage)
      console.log(paginatedResult)

      return jsonResponse(paginatedResult, 200)
    } catch (err) {
      return jsonResponse('Error fetching roles', 500)
    }
  }

  async getRole(req) {
    try {
      const roleId = req.query.get('roleId')
      if (!roleId) {
        return jsonResponse('Role ID is required', 400)
      }

      const result = await roleService.getRoleById(roleId)
      if (!result) {
        return jsonResponse('Role not found', 404)
      }
      return jsonResponse(result, 200)
    } catch (err) {
      return jsonResponse('Error fetching role details', 500)
    }
  }
}

module.exports = new RoleHandler()
