const APIPermissions = [
  // EMR Module
  {
    key: 'emr.patientinfo.view',
    apis: ['patient'],
    methods: ['GET'],
    label_en: 'View Patient Info',
    label_ar: 'عرض معلومات المريض',
  },
  {
    key: 'emr.patientinfo.edit',
    apis: ['patient'],
    methods: ['POST', 'PUT', 'PATCH'],
    label_en: 'Edit Patient Info',
    label_ar: 'تعديل معلومات المريض',
  },
  {
    key: 'emr.patientinfo.search',
    apis: ['patient-search'],
    methods: ['POST'],
    label_en: 'Search Patients',
    label_ar: 'البحث عن المرضى',
  },
  {
    key: 'emr.patientinfo.view.sensitive',
    apis: ['patient/sensitive-info'],
    methods: ['GET'],
    label_en: 'View Sensitive Patient Data',
    label_ar: 'عرض البيانات الحساسة للمريض',
  },
  {
    key: 'emr.patientinfo.view.aadhar',
    apis: ['patient/aadhar'],
    methods: ['GET'],
    label_en: 'View Aadhaar Number',
    label_ar: 'عرض رقم الآذار',
  },
  {
    key: 'emr.patientinfo.view.abha',
    apis: ['patient/abha'],
    methods: ['GET'],
    label_en: 'View ABHA Number',
    label_ar: 'عرض رقم ABHA',
  },
  {
    key: 'emr.consultation.manage',
    apis: ['patient/consulting'],
    methods: ['GET', 'POST', 'PUT'],
    label_en: 'Manage Consultation',
    label_ar: 'إدارة الاستشارة',
  },
  {
    key: 'emr.prescription.view',
    apis: ['prescriptions', 'prescriptions/details'],
    methods: ['GET'],
    label_en: 'View Prescriptions',
    label_ar: 'عرض الوصفات الطبية',
  },
  {
    key: 'emr.prescription.manage',
    apis: ['prescriptions', 'prescriptions/search'],
    methods: ['POST', 'PATCH'],
    label_en: 'Manage Prescriptions',
    label_ar: 'إدارة الوصفات الطبية',
  },
  {
    key: 'emr.reports.manage',
    apis: ['lab-report/upload', 'lab-report/preview'],
    methods: ['POST', 'GET'],
    label_en: 'Manage Reports',
    label_ar: 'إدارة التقارير',
  },
  {
    key: 'emr.doctorprofile.view',
    apis: ['doctor'],
    methods: ['GET'],
    label_en: 'View Doctor Profile',
    label_ar: 'عرض ملف الطبيب',
  },
  {
    key: 'emr.doctorprofile.edit',
    apis: ['doctor'],
    methods: ['POST', 'PUT', 'PATCH'],
    label_en: 'Edit Doctor Profile',
    label_ar: 'تعديل ملف الطبيب',
  },

  // MRD Module
  {
    key: 'mrd.manage-patient.view',
    apis: ['patient/history', 'patient/vitals'],
    methods: ['GET'],
    label_en: 'View Patient Administrative Data',
    label_ar: 'عرض بيانات المريض الإدارية',
  },
  {
    key: 'mrd.manage-patient.edit',
    apis: ['patient-history', 'patient/vitals'],
    methods: ['POST', 'PUT', 'PATCH'],
    label_en: 'Edit Patient Administrative Data',
    label_ar: 'تعديل بيانات المريض الإدارية',
  },
  {
    key: 'mrd.patient-queue.manage',
    apis: ['queue', 'appointment/queue', 'appointment', 'appointment-queue'],
    methods: ['GET', 'POST', 'PATCH'],
    label_en: 'Manage Patient Queue',
    label_ar: 'إدارة قائمة المرضى',
  },

  // Lab Test Module
  {
    key: 'emr.lab-test.view',
    apis: ['lab-tests', 'patient-lab-test/details'],
    methods: ['GET'],
    label_en: 'View Lab Tests',
    label_ar: 'عرض الفحوصات المخبرية',
  },
  {
    key: 'emr.lab-test.manage',
    apis: ['lab-tests', 'patient-lab-test', 'patient-lab-test/search'],
    methods: ['POST', 'PATCH', 'DELETE'],
    label_en: 'Manage Lab Tests',
    label_ar: 'إدارة الفحوصات المخبرية',
  },
  {
    key: 'emr.lab-test.search',
    apis: ['lab-tests/search', 'patient-lab-test/search'],
    methods: ['POST'],
    label_en: 'Search Lab Tests',
    label_ar: 'بحث الفحوصات المخبرية',
  },

  // Test Package Module
  {
    key: 'emr.test-package.view',
    apis: ['package/tests', 'packages/user-specific'],
    methods: ['GET'],
    label_en: 'View Test Packages',
    label_ar: 'عرض حزم الفحوصات',
  },
  {
    key: 'emr.test-package.manage',
    apis: ['package/add-tests', 'package/remove-test'],
    methods: ['POST'],
    label_en: 'Manage Test Packages',
    label_ar: 'إدارة حزم الفحوصات',
  },

  // Prescription Package Module
  {
    key: 'emr.prescription-package.view',
    apis: ['prescription-package', 'prescription-package/medicines'],
    methods: ['GET'],
    label_en: 'View Prescription Packages',
    label_ar: 'عرض حزم الوصفات الطبية',
  },
  {
    key: 'emr.prescription-package.manage',
    apis: ['prescription-package'],
    methods: ['POST', 'PUT', 'DELETE'],
    label_en: 'Manage Prescription Packages',
    label_ar: 'إدارة حزم الوصفات الطبية',
  },
  // Medicine Package Module
  {
    key: 'emr.medicine-package.view',
    apis: ['package', 'package/medicines'],
    methods: ['GET'],
    label_en: 'View Packages',
    label_ar: 'عرض الحزم',
  },
  {
    key: 'emr.medicine-package.manage',
    apis: ['package', 'package/add-medicines', 'package/remove-medicine'],
    methods: ['POST'],
    label_en: 'Manage Packages',
    label_ar: 'إدارة الحزم',
  },
  // Lifestyle Module
  {
    key: 'emr.lifestyle.manage',
    apis: ['patient/lifestyle', 'lifestyle/question'],
    methods: ['GET', 'POST', 'PATCH', 'DELETE'],
    label_en: 'Manage Lifestyle',
    label_ar: 'إدارة نمط الحياة',
  },
  {
    key: 'emr.lifestyle.physical-activity.view',
    apis: ['physical-activity/dashboard'],
    methods: ['GET'],
    label_en: 'View Physical Activity Dashboard',
    label_ar: 'عرض لوحة النشاط البدني',
  },

  // Dashboard Module
  {
    key: 'dashboard.view',
    apis: ['dashboard/summary'],
    methods: ['GET'],
    label_en: 'View Dashboard',
    label_ar: 'عرض لوحة القيادة',
  },

  // Role and Permission Module
  {
    key: 'role.manage',
    apis: ['list-roles', 'role'],
    methods: ['GET', 'POST', 'PATCH', 'DELETE'],
    label_en: 'Manage Roles',
    label_ar: 'إدارة الأدوار',
  },
  {
    key: 'permission.manage',
    apis: ['permissions/api-list', 'assign-permissions'],
    methods: ['GET', 'POST'],
    label_en: 'Manage Permissions',
    label_ar: 'إدارة الأذونات',
  },

  // Organization Module
  {
    key: 'organization.manage',
    apis: ['list-organizations', 'organization'],
    methods: ['GET', 'POST', 'PATCH', 'DELETE'],
    label_en: 'Manage Organizations',
    label_ar: 'إدارة المؤسسات',
  },
  {
    key: 'organization.patients.view',
    apis: ['organization/patients'],
    methods: ['GET'],
    label_en: 'View Organization Patients',
    label_ar: 'عرض مرضى المؤسسة',
  },

  // Consultation Module
  {
    key: 'emr.consultation.view',
    apis: ['patient/consulting', 'book-consultation'],
    methods: ['GET'],
    label_en: 'View Consultation',
    label_ar: 'عرض الاستشارة',
  },
  {
    key: 'emr.consultation.future.view',
    apis: ['book-consultation', 'book-consultation/future'],
    methods: ['GET'],
    label_en: 'View Future Consultation',
    label_ar: 'عرض الاستشارة',
  },
  {
    key: 'emr.consultation.create',
    apis: ['patient/consulting'],
    methods: ['POST'],
    label_en: 'Create Consultation',
    label_ar: 'إنشاء استشارة',
  },
  {
    key: 'emr.consultation.edit',
    apis: ['patient/consulting'],
    methods: ['PUT'],
    label_en: 'Edit Consultation',
    label_ar: 'تعديل الاستشارة',
  },
  // User Management Module
  {
    key: 'user.view',
    apis: ['user', 'user/list'],
    methods: ['GET'],
    label_en: 'View User Information',
    label_ar: 'عرض معلومات المستخدم',
  },
  {
    key: 'user.manage',
    apis: ['user', 'usersignup'],
    methods: ['POST', 'PATCH'],
    label_en: 'Manage Users',
    label_ar: 'إدارة المستخدمين',
  },

  // Payment Module
  {
    key: 'mrd.payment.patient_registration',
    apis: [],
    methods: [],
    label_en: 'Patient Registration Payment',
    label_ar: 'دفع تسجيل المريض',
  },
  {
    key: 'emr.payment.appointment_booking',
    apis: [],
    methods: [],
    label_en: 'Appointment Booking Payment',
    label_ar: 'دفع حجز الموعد',
  },
  {
    key: 'emr.payment.lab_test',
    apis: [],
    methods: [],
    label_en: 'Lab Test Payment',
    label_ar: 'دفع الفحص المخبري',
  },
  {
    key: 'emr.payment.prescription',
    apis: [],
    methods: [],
    label_en: 'Prescription Payment',
    label_ar: 'دفع الوصفة الطبية',
  },
  {
    key: 'payment.create',
    apis: ['payments/create-order', 'payment'],
    methods: ['POST'],
    label_en: 'Create Payment Orders',
    label_ar: 'إنشاء أوامر الدفع',
  },
  {
    key: 'payment.verify',
    apis: ['payments/verify'],
    methods: ['POST'],
    label_en: 'Verify Payments',
    label_ar: 'التحقق من المدفوعات',
  },
  {
    key: 'payment.view',
    apis: ['payments/details', 'payments/organization', 'payment'],
    methods: ['GET'],
    label_en: 'View Payment Details',
    label_ar: 'عرض تفاصيل الدفع',
  },
  {
    key: 'payment.stats',
    apis: ['payments/stats', 'payments/search'],
    methods: ['GET'],
    label_en: 'View Payment Statistics',
    label_ar: 'عرض إحصائيات الدفع',
  },
  {
    key: 'payment.webhook',
    apis: ['payments/webhook'],
    methods: ['POST'],
    label_en: 'Process Payment Webhooks',
    label_ar: 'معالجة webhooks الدفع',
  },

  // Module Access Permissions
  {
    key: 'emr.access',
    apis: [],
    methods: [],
    label_en: 'Access EMR Module',
    label_ar: 'الوصول إلى وحدة EMR',
  },
  {
    key: 'mrd.access',
    apis: [],
    methods: [],
    label_en: 'Access MRD Module',
    label_ar: 'الوصول إلى وحدة MRD',
  },
]

module.exports = { APIPermissions }
